# Task ID: 66
# Title: Support Status Filtering in Show Command for Subtasks
# Status: done
# Dependencies: None
# Priority: medium
# Description: Enhance the 'show' command to accept a status parameter that filters subtasks by their current status, allowing users to view only subtasks matching a specific status.
# Details:
This task involves modifying the existing 'show' command functionality to support status-based filtering of subtasks. Implementation details include:

1. Update the command parser to accept a new '--status' or '-s' flag followed by a status value (e.g., 'task-master show --status=in-progress' or 'task-master show -s completed').

2. Modify the show command handler in the appropriate module (likely in scripts/modules/) to:
   - Parse and validate the status parameter
   - Filter the subtasks collection based on the provided status before displaying results
   - Handle invalid status values gracefully with appropriate error messages
   - Support standard status values (e.g., 'not-started', 'in-progress', 'completed', 'blocked')
   - Consider supporting multiple status values (comma-separated or multiple flags)

3. Update the help documentation to include information about the new status filtering option.

4. Ensure backward compatibility - the show command should function as before when no status parameter is provided.

5. Consider adding a '--status-list' option to display all available status values for reference.

6. Update any relevant unit tests to cover the new functionality.

7. If the application uses a database or persistent storage, ensure the filtering happens at the query level for performance when possible.

8. Maintain consistent formatting and styling of output regardless of filtering.

# Test Strategy:
Testing for this feature should include:

1. Unit tests:
   - Test parsing of the status parameter in various formats (--status=value, -s value)
   - Test filtering logic with different status values
   - Test error handling for invalid status values
   - Test backward compatibility (no status parameter)
   - Test edge cases (empty status, case sensitivity, etc.)

2. Integration tests:
   - Verify that the command correctly filters subtasks when a valid status is provided
   - Verify that all subtasks are shown when no status filter is applied
   - Test with a project containing subtasks of various statuses

3. Manual testing:
   - Create a test project with multiple subtasks having different statuses
   - Run the show command with different status filters and verify results
   - Test with both long-form (--status) and short-form (-s) parameters
   - Verify help documentation correctly explains the new parameter

4. Edge case testing:
   - Test with non-existent status values
   - Test with empty project (no subtasks)
   - Test with a project where all subtasks have the same status

5. Documentation verification:
   - Ensure the README or help documentation is updated to include the new parameter
   - Verify examples in documentation work as expected

All tests should pass before considering this task complete.
