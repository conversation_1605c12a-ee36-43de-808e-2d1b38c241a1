#!/usr/bin/env bun

/**
 * Guidant CLI Entry Point
 * AI Agent Workflow Orchestrator with native Bun TypeScript/JSX support
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Get project root and main CLI path
const projectRoot = join(__dirname, '..');
const mainCLI = join(projectRoot, 'index.js');

try {
  // Import and run the main CLI directly with Bun
  // Bun handles TypeScript/JSX natively, no transpilation needed
  const { runCLI } = await import(mainCLI);

  if (typeof runCLI === 'function') {
    runCLI();
  } else {
    console.error('❌ Failed to load CLI function from index.js');
    process.exit(1);
  }

} catch (error) {
  console.error('❌ Failed to start Guidant with Bun:', error.message);
  console.error('💡 Make sure you have Bun installed: https://bun.sh');
  console.error('💡 Current working directory:', process.cwd());
  console.error('💡 Project root:', projectRoot);

  if (process.env.DEBUG) {
    console.error('🐛 Full error stack:', error.stack);
  }

  process.exit(1);
}
