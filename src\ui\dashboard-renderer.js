/**
 * Dashboard Renderer
 * Professional dashboard rendering using TaskMaster-inspired UI patterns
 */

import chalk from 'chalk';
import boxen from 'boxen';
import Table from 'cli-table3';
import { renderBanner, renderCompactBanner, renderWorkflowBanner, renderSectionHeader } from './components/banner.js';
import { createProgressBar, renderProgressSection } from './components/progress-bar.js';
import {
  getTerminalWidth,
  getAvailableWidth,
  calculateColumnWidth,
  getStatusWithColor,
  getPriorityWithColor,
  getBoxenConfig,
  getTableConfig,
  truncate
} from './shared/terminal-utils.js';
import {
  displayGuidantBanner,
  createStatusBox,
  createProgressDisplay,
  createTaskDisplay,
  createActionSuggestions
} from '../cli/utils/guidant-ui.js';

/**
 * Main dashboard renderer function
 */
export async function renderProjectDashboard(projectState, workflowState, options = {}) {
  const {
    showHeader = true,
    showProgress = true,
    showCapabilities = true,
    showTasks = true,
    compact = false
  } = options;

  let output = '';

  try {
    // Header Section - Use new TaskMaster-style UI
    if (showHeader) {
      displayGuidantBanner({
        version: projectState.config?.version,
        projectName: projectState.config?.name,
        compact: compact,
        clearScreen: false
      });

      // Workflow status using new UI system
      if (projectState.workflow?.adaptive) {
        console.log(createStatusBox(
          `Adaptive Workflow Intelligence Active • ${workflowState.currentPhase?.phase || 'Unknown Phase'} • ${projectState.workflow?.projectType || 'Standard Project'}`,
          'success',
          { title: '✨ Intelligent Orchestration' }
        ));
      } else {
        console.log(createStatusBox(
          'Legacy workflow detected - consider upgrading to adaptive intelligence',
          'warning',
          { title: '⚠️ Workflow Status' }
        ));
      }
    }

    // Progress Section - Use new TaskMaster-style progress display
    if (showProgress && workflowState.phases) {
      const overallProgress = calculateOverallProgress(workflowState.phases);
      const totalPhases = Object.keys(workflowState.phases).length;
      const completedPhases = countCompletedPhases(workflowState.phases);

      console.log(createProgressDisplay(
        workflowState.currentPhase?.phase || 'Unknown Phase',
        {
          current: completedPhases,
          total: totalPhases,
          percentage: overallProgress,
          status: overallProgress >= 100 ? 'completed' : 'in-progress',
          nextPhase: getNextPhase(workflowState.phases, workflowState.currentPhase)
        },
        { compact, showDetails: !compact }
      ));

      // Phase breakdown table (keep existing implementation for detailed view)
      if (!compact) {
        renderPhaseBreakdown(workflowState.phases, workflowState.currentPhase);
      }
    }

    // Capabilities Section
    if (showCapabilities) {
      renderCapabilitiesSection(projectState, { compact });
    }

    // Tasks Section
    if (showTasks) {
      await renderTasksSection(projectState, workflowState, { compact });
    }

    return output;

  } catch (error) {
    console.error(chalk.red('❌ Dashboard rendering error:'), error.message);
    return renderErrorDashboard(error);
  }
}

/**
 * Calculate overall project progress
 */
function calculateOverallProgress(phases) {
  if (!phases || Object.keys(phases).length === 0) return 0;

  const phaseEntries = Object.entries(phases);
  const completedPhases = phaseEntries.filter(([_, phase]) => phase.completed).length;
  
  return Math.round((completedPhases / phaseEntries.length) * 100);
}

/**
 * Count completed phases
 */
function countCompletedPhases(phases) {
  if (!phases) return 0;
  return Object.values(phases).filter(phase => phase.completed).length;
}

/**
 * Get next phase in workflow
 */
function getNextPhase(phases, currentPhase) {
  if (!phases || !currentPhase) return null;

  const phaseNames = Object.keys(phases);
  const currentIndex = phaseNames.indexOf(currentPhase.phase);

  if (currentIndex >= 0 && currentIndex < phaseNames.length - 1) {
    return phaseNames[currentIndex + 1];
  }

  return null;
}

/**
 * Render phase breakdown
 */
function renderPhaseBreakdown(phases, currentPhase) {
  renderSectionHeader('Phase Progress', { icon: '🔄', compact: true });

  const terminalWidth = getTerminalWidth();
  const nameWidth = calculateColumnWidth(30, terminalWidth);
  const statusWidth = calculateColumnWidth(20, terminalWidth);
  const progressWidth = calculateColumnWidth(50, terminalWidth);

  const table = new Table({
    ...getTableConfig([nameWidth, statusWidth, progressWidth]),
    head: [
      chalk.cyan.bold('Phase'),
      chalk.cyan.bold('Status'),
      chalk.cyan.bold('Progress')
    ]
  });

  Object.entries(phases).forEach(([phaseName, phase]) => {
    const isCurrent = currentPhase?.phase === phaseName;
    const status = phase.completed ? 'completed' : (isCurrent ? 'in-progress' : 'pending');
    const progress = phase.progress || 0;

    table.push([
      isCurrent ? chalk.yellow.bold(`► ${phaseName}`) : phaseName,
      getStatusWithColor(status, true),
      createProgressBar(progress, 20)
    ]);
  });

  console.log(table.toString());
  console.log();
}

/**
 * Render capabilities section
 */
function renderCapabilitiesSection(projectState, options = {}) {
  const { compact } = options;

  renderSectionHeader('AI Capabilities', { icon: '🤖', compact });

  // Mock capabilities data - replace with actual data from projectState
  const capabilities = projectState.ai?.capabilities || {
    totalTools: 16,
    categories: 5,
    coverage: 85
  };

  if (compact) {
    console.log(`Tools: ${capabilities.totalTools} • Categories: ${capabilities.categories} • Coverage: ${capabilities.coverage}%`);
  } else {
    const capabilityBox = boxen(
      `${chalk.cyan('Available Tools:')} ${capabilities.totalTools}\n` +
      `${chalk.cyan('Categories:')} ${capabilities.categories}\n` +
      `${chalk.cyan('Coverage:')} ${createProgressBar(capabilities.coverage, 20)}`,
      {
        ...getBoxenConfig('section'),
        borderColor: 'blue'
      }
    );
    console.log(capabilityBox);
  }

  console.log();
}

/**
 * Render tasks section using new TaskMaster-style UI
 */
async function renderTasksSection(projectState, workflowState, options = {}) {
  const { compact } = options;

  // Mock task data - replace with actual task generation
  const currentTask = {
    id: 'TASK-001',
    title: 'Implement TaskMaster-Style UI System',
    priority: 'high',
    status: 'in-progress',
    phase: workflowState.currentPhase?.phase || 'implementation',
    description: 'Create professional boxen-based visual hierarchy for consistent CLI output'
  };

  // Use new TaskMaster-style task display
  console.log(createTaskDisplay(
    currentTask,
    {
      currentPhase: workflowState.currentPhase?.phase,
      totalPhases: Object.keys(workflowState.phases || {}).length,
      workflowType: projectState.workflow?.projectType
    },
    { compact, showContext: !compact }
  ));

  // Add TaskMaster-style action suggestions
  if (!compact) {
    const suggestedActions = [
      { description: 'Continue implementing UI components', command: 'guidant status --compact' },
      { description: 'Test new UI system integration', command: 'guidant init --help' },
      { description: 'Review progress and advance phase', command: 'guidant adaptive --status' }
    ];

    console.log(createActionSuggestions(suggestedActions, {
      title: 'Suggested Next Actions',
      maxActions: 3
    }));
  }
}

/**
 * Render error dashboard using TaskMaster-style error display
 */
function renderErrorDashboard(error) {
  const errorDisplay = createStatusBox(
    `${error.message}\n\nPlease check your project configuration and try again.`,
    'error',
    { title: 'Dashboard Error' }
  );

  console.log(errorDisplay);
  return errorDisplay;
}
