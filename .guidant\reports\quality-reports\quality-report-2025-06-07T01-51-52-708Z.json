{"id": "quality-1749261112761", "type": "quality", "generatedAt": "2025-06-07T01:51:52.708Z", "projectName": "", "qualityScore": 20, "assessment": {"completeness": {"score": 0, "completed": 0, "total": 12, "status": "poor"}, "consistency": {"score": 0, "status": "no-data"}, "documentation": {"score": 0, "documentationSessions": 0, "status": "poor"}, "testCoverage": {"score": 0, "testingSessions": 0, "status": "poor"}}, "phaseQuality": {"concept": {"completionRate": 0, "completed": 0, "required": 2, "status": "pending"}, "requirements": {"completionRate": 0, "completed": 0, "required": 2, "status": "pending"}, "design": {"completionRate": 0, "completed": 0, "required": 2, "status": "pending"}, "architecture": {"completionRate": 0, "completed": 0, "required": 2, "status": "pending"}, "implementation": {"completionRate": 0, "completed": 0, "required": 2, "status": "pending"}, "deployment": {"completionRate": 0, "completed": 0, "required": 2, "status": "pending"}}, "issues": [{"type": "incomplete-deliverables", "phase": "concept", "severity": "medium", "message": "concept phase missing 2 deliverables"}, {"type": "incomplete-deliverables", "phase": "requirements", "severity": "medium", "message": "requirements phase missing 2 deliverables"}, {"type": "incomplete-deliverables", "phase": "design", "severity": "medium", "message": "design phase missing 2 deliverables"}, {"type": "incomplete-deliverables", "phase": "architecture", "severity": "medium", "message": "architecture phase missing 2 deliverables"}, {"type": "incomplete-deliverables", "phase": "implementation", "severity": "medium", "message": "implementation phase missing 2 deliverables"}, {"type": "incomplete-deliverables", "phase": "deployment", "severity": "medium", "message": "deployment phase missing 2 deliverables"}], "recommendations": [{"type": "action", "priority": "high", "message": "Complete missing deliverables in concept phase"}, {"type": "action", "priority": "high", "message": "Complete missing deliverables in requirements phase"}, {"type": "action", "priority": "high", "message": "Complete missing deliverables in design phase"}, {"type": "action", "priority": "high", "message": "Complete missing deliverables in architecture phase"}, {"type": "action", "priority": "high", "message": "Complete missing deliverables in implementation phase"}, {"type": "action", "priority": "high", "message": "Complete missing deliverables in deployment phase"}]}