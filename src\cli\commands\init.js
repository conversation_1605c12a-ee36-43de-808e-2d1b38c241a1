/**
 * Init Command
 * Initialize a new Guidant project
 */

import chalk from 'chalk';
import inquirer from 'inquirer';
import { initializeProjectStructure, isProjectInitialized, updateProjectFile } from '../../file-management/project-structure.js';
import { handleError, showSuccess, showWarning } from '../utils.js';
import {
  displayGuidantBanner,
  createStatusBox,
  createProgressiveOnboarding
} from '../utils/guidant-ui.js';

/**
 * Init command implementation
 */
export async function initCommand(options = {}) {
  try {
    // Display professional banner using new TaskMaster-style UI system
    displayGuidantBanner({
      clearScreen: !options.noClear,
      compact: options.compact
    });

    // Check if already initialized
    if (await isProjectInitialized()) {
      console.log(createStatusBox(
        'Guidant project already initialized in this directory.',
        'warning',
        { title: 'Project Already Exists' }
      ));

      if (!options.force) {
        const { proceed } = await inquirer.prompt([{
          type: 'confirm',
          name: 'proceed',
          message: 'Reinitialize project? This will reset all configuration.',
          default: false
        }]);

        if (!proceed) {
          console.log(createStatusBox('Initialization cancelled.', 'info'));
          return;
        }
      }
    }

    // Get project details
    let projectName = options.name;
    let projectDescription = options.description;

    if (!options.yes && !projectName) {
      const answers = await inquirer.prompt([
        {
          type: 'input',
          name: 'name',
          message: 'Project name:',
          default: 'My Guidant Project',
          validate: input => input.trim().length > 0 || 'Project name is required'
        },
        {
          type: 'input',
          name: 'description',
          message: 'Project description:',
          default: 'A new project built with Guidant'
        }
      ]);
      
      projectName = answers.name;
      projectDescription = answers.description;
    }

    // Use defaults if not provided
    projectName = projectName || 'My Guidant Project';
    projectDescription = projectDescription || 'A new project built with Guidant';

    console.log(createStatusBox(
      'Initializing Guidant project structure and configuration...',
      'progress',
      { title: 'Setup in Progress' }
    ));

    // Initialize project structure
    await initializeProjectStructure(process.cwd());

    // Update project config with name and description
    await updateProjectFile('.guidant/project/config.json', (config) => ({
      ...config,
      name: projectName,
      description: projectDescription,
      lastModified: new Date().toISOString()
    }), process.cwd());

    console.log(createStatusBox(
      'Guidant project initialized successfully! Your AI workflow orchestrator is ready.',
      'success',
      { title: 'Initialization Complete' }
    ));

    // Display TaskMaster-style progressive onboarding
    const guidantSteps = [
      { description: 'Run "guidant status" to see project overview', command: 'guidant status' },
      { description: 'Analyze AI agent capabilities', command: 'guidant test-capabilities' },
      { description: 'Configure workflow settings', command: 'guidant adaptive --configure' },
      { description: 'Create project requirements document' },
      { description: 'Generate initial workflow tasks' },
      { description: 'Begin systematic development workflow' },
      { description: 'Complete current phase deliverables' },
      { description: 'Advance through workflow phases' },
      { description: 'Iterate based on feedback and learnings' },
      { description: 'Ship it!' }
    ];

    console.log(createProgressiveOnboarding(guidantSteps, {
      title: 'Things you should do next'
    }));

  } catch (error) {
    handleError(error, 'Init command');
  }
}

/**
 * Register init command with commander
 */
export function registerInitCommand(program) {
  program
    .command('init')
    .description('Initialize a new Guidant project')
    .option('-n, --name <name>', 'Project name')
    .option('-d, --description <description>', 'Project description')
    .option('-y, --yes', 'Skip interactive prompts and use defaults')
    .option('-f, --force', 'Force reinitialize if project already exists')
    .action(initCommand);
}
