{"anthropic": [{"id": "claude-sonnet-4-20250514", "name": "<PERSON> 4", "swe_score": 0.727, "cost_per_1m_tokens": {"input": 3.0, "output": 15.0}, "allowed_roles": ["main", "analysis", "generation", "fallback"], "max_tokens": 64000}, {"id": "claude-opus-4-20250514", "name": "<PERSON> 4", "swe_score": 0.725, "cost_per_1m_tokens": {"input": 15.0, "output": 75.0}, "allowed_roles": ["main", "analysis", "fallback"], "max_tokens": 32000}, {"id": "claude-3-7-sonnet-20250219", "name": "Claude 3.7 Sonnet", "swe_score": 0.623, "cost_per_1m_tokens": {"input": 3.0, "output": 15.0}, "allowed_roles": ["main", "analysis", "generation", "fallback"], "max_tokens": 120000}, {"id": "claude-3-5-sonnet-20241022", "name": "Claude 3.5 Sonnet", "swe_score": 0.49, "cost_per_1m_tokens": {"input": 3.0, "output": 15.0}, "allowed_roles": ["main", "analysis", "generation", "fallback"], "max_tokens": 64000}], "openai": [{"id": "o3", "name": "OpenAI O3", "swe_score": 0.5, "cost_per_1m_tokens": {"input": 10.0, "output": 40.0}, "allowed_roles": ["main", "analysis", "fallback"], "max_tokens": 200000}, {"id": "o3-mini", "name": "OpenAI O3 Mini", "swe_score": 0.493, "cost_per_1m_tokens": {"input": 1.1, "output": 4.4}, "allowed_roles": ["main", "analysis", "generation"], "max_tokens": 100000}, {"id": "o1", "name": "OpenAI O1", "swe_score": 0.489, "cost_per_1m_tokens": {"input": 15.0, "output": 60.0}, "allowed_roles": ["main", "analysis"], "max_tokens": 200000}, {"id": "o4-mini", "name": "OpenAI O4 Mini", "swe_score": 0.45, "cost_per_1m_tokens": {"input": 1.1, "output": 4.4}, "allowed_roles": ["main", "analysis", "fallback"], "max_tokens": 100000}, {"id": "o1-mini", "name": "OpenAI O1 Mini", "swe_score": 0.4, "cost_per_1m_tokens": {"input": 1.1, "output": 4.4}, "allowed_roles": ["main", "analysis"], "max_tokens": 100000}, {"id": "gpt-4-5-preview", "name": "GPT-4.5 Preview", "swe_score": 0.38, "cost_per_1m_tokens": {"input": 75.0, "output": 150.0}, "allowed_roles": ["main", "analysis"], "max_tokens": 200000}, {"id": "gpt-4o", "name": "GPT-4o", "swe_score": 0.332, "cost_per_1m_tokens": {"input": 2.5, "output": 10.0}, "allowed_roles": ["main", "analysis", "fallback"], "max_tokens": 16384}, {"id": "gpt-4o-search-preview", "name": "GPT-4o Search Preview", "swe_score": 0.33, "cost_per_1m_tokens": {"input": 2.5, "output": 10.0}, "allowed_roles": ["research"], "max_tokens": 16384}, {"id": "gpt-4o-mini", "name": "GPT-4o Mini", "swe_score": 0.3, "cost_per_1m_tokens": {"input": 0.15, "output": 0.6}, "allowed_roles": ["main", "fallback", "generation"], "max_tokens": 16384}, {"id": "gpt-4o-mini-search-preview", "name": "GPT-4o Mini Search Preview", "swe_score": 0.3, "cost_per_1m_tokens": {"input": 0.15, "output": 0.6}, "allowed_roles": ["research"], "max_tokens": 16384}, {"id": "gpt-4-1-mini", "name": "GPT-4.1 Mini", "swe_score": 0, "cost_per_1m_tokens": {"input": 0.4, "output": 1.6}, "allowed_roles": ["main", "fallback", "generation"], "max_tokens": 1000000}, {"id": "gpt-4-1-nano", "name": "GPT-4.1 Nano", "swe_score": 0, "cost_per_1m_tokens": {"input": 0.1, "output": 0.4}, "allowed_roles": ["fallback", "generation"], "max_tokens": 1000000}, {"id": "o1-pro", "name": "OpenAI O1 Pro", "swe_score": 0, "cost_per_1m_tokens": {"input": 150.0, "output": 600.0}, "allowed_roles": ["main", "analysis"], "max_tokens": 100000}], "google": [{"id": "gemini-2.0-flash", "name": "Gemini 2.0 Flash", "swe_score": 0.754, "cost_per_1m_tokens": {"input": 0.15, "output": 0.6}, "allowed_roles": ["main", "analysis", "generation", "fallback"], "max_tokens": 1048000}, {"id": "gemini-2.5-pro-preview-05-06", "name": "Gemini 2.5 Pro Preview", "swe_score": 0.638, "cost_per_1m_tokens": null, "allowed_roles": ["main", "analysis", "fallback"], "max_tokens": 1048000}, {"id": "gemini-2.5-pro-preview-03-25", "name": "Gemini 2.5 Pro Preview (March)", "swe_score": 0.638, "cost_per_1m_tokens": null, "allowed_roles": ["main", "analysis", "fallback"], "max_tokens": 1048000}, {"id": "gemini-2.5-flash-preview-04-17", "name": "Gemini 2.5 Flash Preview", "swe_score": 0, "cost_per_1m_tokens": null, "allowed_roles": ["main", "generation", "fallback"], "max_tokens": 1048000}, {"id": "gemini-2.0-flash-lite", "name": "Gemini 2.0 Flash Lite", "swe_score": 0, "cost_per_1m_tokens": null, "allowed_roles": ["fallback", "generation"], "max_tokens": 1048000}], "perplexity": [{"id": "sonar-pro", "name": "Perplexity Sonar Pro", "swe_score": 0, "cost_per_1m_tokens": {"input": 3, "output": 15}, "allowed_roles": ["research"], "max_tokens": 8700}, {"id": "sonar", "name": "Perplexity Sonar", "swe_score": 0, "cost_per_1m_tokens": {"input": 1, "output": 1}, "allowed_roles": ["research"], "max_tokens": 8700}, {"id": "deep-research", "name": "Perplexity Deep Research", "swe_score": 0.211, "cost_per_1m_tokens": {"input": 2, "output": 8}, "allowed_roles": ["research"], "max_tokens": 8700}, {"id": "sonar-reasoning-pro", "name": "Perplexity Sonar Reasoning Pro", "swe_score": 0.211, "cost_per_1m_tokens": {"input": 2, "output": 8}, "allowed_roles": ["main", "analysis", "fallback"], "max_tokens": 8700}, {"id": "sonar-reasoning", "name": "Perplexity Sonar Reasoning", "swe_score": 0.211, "cost_per_1m_tokens": {"input": 1, "output": 5}, "allowed_roles": ["main", "analysis", "fallback"], "max_tokens": 8700}], "xai": [{"id": "grok-3", "name": "Grok 3", "swe_score": null, "cost_per_1m_tokens": {"input": 3, "output": 15}, "allowed_roles": ["main", "analysis", "generation", "research", "fallback"], "max_tokens": 131072}, {"id": "grok-3-fast", "name": "Grok 3 Fast", "swe_score": 0, "cost_per_1m_tokens": {"input": 5, "output": 25}, "allowed_roles": ["main", "analysis", "generation", "research", "fallback"], "max_tokens": 131072}], "ollama": [{"id": "devstral:latest", "name": "Dev<PERSON><PERSON> Latest", "swe_score": 0, "cost_per_1m_tokens": {"input": 0, "output": 0}, "allowed_roles": ["main", "analysis", "fallback"], "max_tokens": 32768}, {"id": "qwen3:latest", "name": "Qwen 3 Latest", "swe_score": 0, "cost_per_1m_tokens": {"input": 0, "output": 0}, "allowed_roles": ["main", "fallback", "generation"], "max_tokens": 32768}, {"id": "qwen3:14b", "name": "Qwen 3 14B", "swe_score": 0, "cost_per_1m_tokens": {"input": 0, "output": 0}, "allowed_roles": ["main", "fallback", "generation"], "max_tokens": 32768}, {"id": "qwen3:32b", "name": "<PERSON>wen 3 32B", "swe_score": 0, "cost_per_1m_tokens": {"input": 0, "output": 0}, "allowed_roles": ["main", "analysis", "fallback"], "max_tokens": 32768}, {"id": "mistral-small3.1:latest", "name": "Mistral Small 3.1 Latest", "swe_score": 0, "cost_per_1m_tokens": {"input": 0, "output": 0}, "allowed_roles": ["main", "fallback", "generation"], "max_tokens": 32768}, {"id": "llama3.3:latest", "name": "Llama 3.3 Latest", "swe_score": 0, "cost_per_1m_tokens": {"input": 0, "output": 0}, "allowed_roles": ["main", "fallback", "generation"], "max_tokens": 32768}, {"id": "phi4:latest", "name": "Phi 4 Latest", "swe_score": 0, "cost_per_1m_tokens": {"input": 0, "output": 0}, "allowed_roles": ["main", "fallback", "generation"], "max_tokens": 32768}], "openrouter": [{"id": "google/gemini-2.5-flash-preview-05-20", "name": "Gemini 2.5 Flash Preview (OpenRouter)", "swe_score": 0, "cost_per_1m_tokens": {"input": 0.15, "output": 0.6}, "allowed_roles": ["main", "generation", "fallback"], "max_tokens": 1048576}, {"id": "google/gemini-2.5-flash-preview-05-20:thinking", "name": "Gemini 2.5 Flash Thinking (OpenRouter)", "swe_score": 0, "cost_per_1m_tokens": {"input": 0.15, "output": 3.5}, "allowed_roles": ["main", "analysis", "fallback"], "max_tokens": 1048576}, {"id": "google/gemini-2.5-pro-exp-03-25", "name": "Gemini 2.5 Pro Experimental (OpenRouter)", "swe_score": 0, "cost_per_1m_tokens": {"input": 0, "output": 0}, "allowed_roles": ["main", "analysis", "fallback"], "max_tokens": 1000000}, {"id": "google/gemini-2.0-flash-exp:free", "name": "Gemini 2.0 Flash Experimental Free (OpenRouter)", "swe_score": 0.754, "cost_per_1m_tokens": {"input": 0, "output": 0}, "allowed_roles": ["main", "analysis", "generation", "fallback"], "max_tokens": 1048000}, {"id": "deepseek/deepseek-chat-v3-0324:free", "name": "DeepSeek Chat V3 Free (OpenRouter)", "swe_score": 0, "cost_per_1m_tokens": {"input": 0, "output": 0}, "allowed_roles": ["main", "fallback", "generation"], "max_tokens": 163840}, {"id": "deepseek/deepseek-chat-v3-0324", "name": "DeepSeek Chat V3 (OpenRouter)", "swe_score": 0, "cost_per_1m_tokens": {"input": 0.27, "output": 1.1}, "allowed_roles": ["main", "analysis"], "max_tokens": 64000}, {"id": "openai/gpt-4.1", "name": "GPT-4.1 (OpenRouter)", "swe_score": 0, "cost_per_1m_tokens": {"input": 2, "output": 8}, "allowed_roles": ["main", "analysis", "fallback"], "max_tokens": 1000000}, {"id": "openai/gpt-4.1-mini", "name": "GPT-4.1 Mini (OpenRouter)", "swe_score": 0, "cost_per_1m_tokens": {"input": 0.4, "output": 1.6}, "allowed_roles": ["main", "fallback", "generation"], "max_tokens": 1000000}, {"id": "openai/gpt-4.1-nano", "name": "GPT-4.1 <PERSON><PERSON> (OpenRouter)", "swe_score": 0, "cost_per_1m_tokens": {"input": 0.1, "output": 0.4}, "allowed_roles": ["fallback", "generation"], "max_tokens": 1000000}, {"id": "openai/o3", "name": "OpenAI O3 (OpenRouter)", "swe_score": 0, "cost_per_1m_tokens": {"input": 10, "output": 40}, "allowed_roles": ["main", "analysis", "fallback"], "max_tokens": 200000}, {"id": "openai/codex-mini", "name": "OpenAI Codex Mini (OpenRouter)", "swe_score": 0, "cost_per_1m_tokens": {"input": 1.5, "output": 6}, "allowed_roles": ["main", "analysis", "generation"], "max_tokens": 100000}, {"id": "openai/gpt-4o-mini", "name": "GPT-4o Mini (OpenRouter)", "swe_score": 0, "cost_per_1m_tokens": {"input": 0.15, "output": 0.6}, "allowed_roles": ["main", "fallback", "generation"], "max_tokens": 100000}, {"id": "openai/o4-mini", "name": "OpenAI O4 Mini (OpenRouter)", "swe_score": 0.45, "cost_per_1m_tokens": {"input": 1.1, "output": 4.4}, "allowed_roles": ["main", "analysis", "fallback"], "max_tokens": 100000}, {"id": "openai/o4-mini-high", "name": "OpenAI O4 Mini High (OpenRouter)", "swe_score": 0, "cost_per_1m_tokens": {"input": 1.1, "output": 4.4}, "allowed_roles": ["main", "analysis", "fallback"], "max_tokens": 100000}, {"id": "openai/o1-pro", "name": "OpenAI O1 Pro (OpenRouter)", "swe_score": 0, "cost_per_1m_tokens": {"input": 150, "output": 600}, "allowed_roles": ["main", "analysis"], "max_tokens": 100000}, {"id": "meta-llama/llama-3.3-70b-instruct", "name": "Llama 3.3 70B Instruct (OpenRouter)", "swe_score": 0, "cost_per_1m_tokens": {"input": 120, "output": 600}, "allowed_roles": ["main", "analysis", "fallback"], "max_tokens": 1048576}, {"id": "meta-llama/llama-4-maverick", "name": "Llama 4 Maverick (OpenRouter)", "swe_score": 0, "cost_per_1m_tokens": {"input": 0.18, "output": 0.6}, "allowed_roles": ["main", "analysis", "fallback"], "max_tokens": 1000000}, {"id": "meta-llama/llama-4-scout", "name": "Llama 4 Scout (OpenRouter)", "swe_score": 0, "cost_per_1m_tokens": {"input": 0.08, "output": 0.3}, "allowed_roles": ["main", "fallback", "generation"], "max_tokens": 1000000}, {"id": "qwen/qwen-max", "name": "<PERSON><PERSON> (OpenRouter)", "swe_score": 0, "cost_per_1m_tokens": {"input": 1.6, "output": 6.4}, "allowed_roles": ["main", "analysis", "fallback"], "max_tokens": 32768}, {"id": "qwen/qwen-turbo", "name": "<PERSON><PERSON> (OpenRouter)", "swe_score": 0, "cost_per_1m_tokens": {"input": 0.05, "output": 0.2}, "allowed_roles": ["main", "fallback", "generation"], "max_tokens": 1000000}, {"id": "qwen/qwen3-235b-a22b", "name": "Qwen 3 235B (OpenRouter)", "swe_score": 0, "cost_per_1m_tokens": {"input": 0.14, "output": 2}, "allowed_roles": ["main", "analysis", "fallback"], "max_tokens": 24000}, {"id": "mistralai/mistral-small-3.1-24b-instruct:free", "name": "Mistral Small 3.1 Free (OpenRouter)", "swe_score": 0, "cost_per_1m_tokens": {"input": 0, "output": 0}, "allowed_roles": ["main", "fallback", "generation"], "max_tokens": 96000}, {"id": "mistralai/mistral-small-3.1-24b-instruct", "name": "Mistral Small 3.1 (OpenRouter)", "swe_score": 0, "cost_per_1m_tokens": {"input": 0.1, "output": 0.3}, "allowed_roles": ["main", "fallback", "generation"], "max_tokens": 128000}, {"id": "mistralai/devstral-small", "name": "<PERSON><PERSON><PERSON> (OpenRouter)", "swe_score": 0, "cost_per_1m_tokens": {"input": 0.1, "output": 0.3}, "allowed_roles": ["main", "analysis", "generation"], "max_tokens": 110000}, {"id": "mistralai/mistral-nemo", "name": "<PERSON><PERSON><PERSON> (OpenRouter)", "swe_score": 0, "cost_per_1m_tokens": {"input": 0.03, "output": 0.07}, "allowed_roles": ["main", "fallback", "generation"], "max_tokens": 100000}, {"id": "thudm/glm-4-32b:free", "name": "GLM-4 32B Free (OpenRouter)", "swe_score": 0, "cost_per_1m_tokens": {"input": 0, "output": 0}, "allowed_roles": ["main", "fallback", "generation"], "max_tokens": 32768}], "mistral": [{"id": "mistral-large-latest", "name": "Mistral Large Latest", "swe_score": 0.25, "cost_per_1m_tokens": {"input": 2.0, "output": 6.0}, "allowed_roles": ["main", "analysis", "generation", "fallback"], "max_tokens": 32768}, {"id": "mistral-medium-latest", "name": "Mistral Medium Latest", "swe_score": 0.2, "cost_per_1m_tokens": {"input": 2.7, "output": 8.1}, "allowed_roles": ["main", "fallback"], "max_tokens": 32768}]}