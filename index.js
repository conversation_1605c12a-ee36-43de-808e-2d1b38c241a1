#!/usr/bin/env bun

/**
 * Guidant
 * AI Agent Workflow Orchestrator for systematic software development
 *
 * Clean entry point that delegates to the modular CLI structure
 * Optimized for Bun runtime with native TypeScript/JSX support
 */

import { runCLI } from './src/cli/app.js';
import { readFileSync } from 'fs';

// Export version information for programmatic access
const packageJson = JSON.parse(readFileSync('./package.json', 'utf8'));
export const version = packageJson.version;

// Export runCLI for bin/guidant.js
export { runCLI };

// Run CLI if this file is executed directly
// Bun-compatible main detection
if (import.meta.main || (process.argv[1] && process.argv[1].endsWith('index.js'))) {
  runCLI();
}
