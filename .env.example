# Guidant Evolution Environment Configuration

# =============================================================================
# AI PROVIDER API KEYS
# =============================================================================

# OpenRouter API Key (Primary provider - supports multiple models)
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Perplexity API Key (Research and web search tasks)
PERPLEXITY_API_KEY=your_perplexity_api_key_here

# Anthropic API Key (Direct Claude access)
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# OpenAI API Key (GPT models)
OPENAI_API_KEY=your_openai_api_key_here

# Google API Key (Gemini models)
GOOGLE_API_KEY=your_google_api_key_here

# Mistral API Key (Alternative provider)
MISTRAL_API_KEY=your_mistral_api_key_here

# xAI API Key (Grok models)
XAI_API_KEY=your_xai_api_key_here

# Ollama API Key (Local models - optional)
OLLAMA_API_KEY=your_ollama_api_key_here

# =============================================================================
# AI MODEL CONFIGURATION BY ROLE
# =============================================================================

# Main Model (General AI tasks)
AI_MAIN_PROVIDER=openrouter
AI_MAIN_MODEL=anthropic/claude-3.5-sonnet
AI_MAIN_TEMPERATURE=0.7
AI_MAIN_MAX_TOKENS=4000

# Analysis Model (Code analysis, reasoning tasks)
AI_ANALYSIS_PROVIDER=openrouter
AI_ANALYSIS_MODEL=anthropic/claude-3.5-sonnet
AI_ANALYSIS_TEMPERATURE=0.2
AI_ANALYSIS_MAX_TOKENS=8000

# Research Model (Web search, research tasks)
AI_RESEARCH_PROVIDER=perplexity
AI_RESEARCH_MODEL=sonar-pro
AI_RESEARCH_TEMPERATURE=0.1
AI_RESEARCH_MAX_TOKENS=8000

# Generation Model (Creative tasks, content generation)
AI_GENERATION_PROVIDER=openrouter
AI_GENERATION_MODEL=anthropic/claude-3.5-sonnet
AI_GENERATION_TEMPERATURE=0.8
AI_GENERATION_MAX_TOKENS=2000

# Fallback Model (When primary models fail)
AI_FALLBACK_PROVIDER=openrouter
AI_FALLBACK_MODEL=anthropic/claude-3-haiku
AI_FALLBACK_TEMPERATURE=0.7
AI_FALLBACK_MAX_TOKENS=2000

# =============================================================================
# LEGACY COMPATIBILITY (Deprecated - use role-specific configs above)
# =============================================================================

# Legacy model configuration (for backward compatibility)
AI_MODEL=anthropic/claude-3.5-sonnet
AI_TEMPERATURE=0.7
AI_MAX_TOKENS=1000

# =============================================================================
# CLOUD PROVIDER CONFIGURATION
# =============================================================================

# Google Cloud / Vertex AI Configuration
GOOGLE_CLOUD_PROJECT=your_project_id
VERTEX_AI_LOCATION=us-central1
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account.json

# Ollama Configuration (Local models)
OLLAMA_BASE_URL=http://localhost:11434/api

# =============================================================================
# GUIDANT CONFIGURATION
# =============================================================================

# Debug and logging
GUIDANT_DEBUG=false
GUIDANT_LOG_LEVEL=info

# Legacy TaskMaster compatibility
TASKMASTER_DEBUG=false
TASKMASTER_LOG_LEVEL=info
