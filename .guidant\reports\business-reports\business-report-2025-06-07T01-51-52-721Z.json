{"id": "business-1749261112769", "type": "business", "generatedAt": "2025-06-07T01:51:52.721Z", "projectName": "", "executiveSummary": {"projectStatus": "needs-attention", "overallProgress": 0, "qualityScore": 20, "keyAchievements": [], "criticalPath": ["concept", "requirements"], "nextMilestone": "requirements"}, "milestones": {"completed": [], "upcoming": [{"phase": "requirements", "deliverables": ["prd_complete", "user_stories"], "estimatedDuration": 7}, {"phase": "design", "deliverables": ["wireframes", "user_flows"], "estimatedDuration": 10}, {"phase": "architecture", "deliverables": ["system_design", "tech_stack"], "estimatedDuration": 8}], "delayed": []}, "riskAssessment": [{"type": "quality", "severity": "medium", "description": "Current phase (concept) is less than 50% complete"}], "resourceUtilization": {"totalSessions": 0, "averageSessionsPerWeek": 0, "mostActivePhase": "none", "efficiency": 0}, "businessValue": {"deliveredValue": 0, "potentialValue": 12, "valueRealization": 0, "businessImpact": "minimal"}, "decisions": [], "recommendations": [{"type": "strategic", "priority": "high", "message": "Focus on completing concept phase to establish strong foundation"}]}