# Task ID: 55
# Title: Implement Positional Arguments Support for CLI Commands
# Status: pending
# Dependencies: None
# Priority: medium
# Description: Upgrade CLI commands to support positional arguments alongside the existing flag-based syntax, allowing for more intuitive command usage.
# Details:
This task involves modifying the command parsing logic in commands.js to support positional arguments as an alternative to the current flag-based approach. The implementation should:

1. Update the argument parsing logic to detect when arguments are provided without flag prefixes (--)
2. Map positional arguments to their corresponding parameters based on their order
3. For each command in commands.js, define a consistent positional argument order (e.g., for set-status: first arg = id, second arg = status)
4. Maintain backward compatibility with the existing flag-based syntax
5. Handle edge cases such as:
   - Commands with optional parameters
   - Commands with multiple parameters
   - Commands that accept arrays or complex data types
6. Update the help text for each command to show both usage patterns
7. Modify the cursor rules to work with both input styles
8. Ensure error messages are clear when positional arguments are provided incorrectly

Example implementations:
- `task-master set-status 25 done` should be equivalent to `task-master set-status --id=25 --status=done`
- `task-master add-task "New task name" "Task description"` should be equivalent to `task-master add-task --name="New task name" --description="Task description"`

The code should prioritize maintaining the existing functionality while adding this new capability.

# Test Strategy:
Testing should verify both the new positional argument functionality and continued support for flag-based syntax:

1. Unit tests:
   - Create tests for each command that verify it works with both positional and flag-based arguments
   - Test edge cases like missing arguments, extra arguments, and mixed usage (some positional, some flags)
   - Verify help text correctly displays both usage patterns

2. Integration tests:
   - Test the full CLI with various commands using both syntax styles
   - Verify that output is identical regardless of which syntax is used
   - Test commands with different numbers of arguments

3. Manual testing:
   - Run through a comprehensive set of real-world usage scenarios with both syntax styles
   - Verify cursor behavior works correctly with both input methods
   - Check that error messages are helpful when incorrect positional arguments are provided

4. Documentation verification:
   - Ensure README and help text accurately reflect the new dual syntax support
   - Verify examples in documentation show both styles where appropriate

All tests should pass with 100% of commands supporting both argument styles without any regression in existing functionality.

# Subtasks:
## 1. Analyze current CLI argument parsing structure [pending]
### Dependencies: None
### Description: Review the existing CLI argument parsing code to understand how arguments are currently processed and identify integration points for positional arguments.
### Details:
Document the current argument parsing flow, identify key classes and methods responsible for argument handling, and determine how named arguments are currently processed. Create a technical design document outlining the current architecture and proposed changes.

## 2. Design positional argument specification format [pending]
### Dependencies: 55.1
### Description: Create a specification for how positional arguments will be defined in command definitions, including their order, required/optional status, and type validation.
### Details:
Define a clear syntax for specifying positional arguments in command definitions. Consider how to handle mixed positional and named arguments, default values, and type constraints. Document the specification with examples for different command types.

## 3. Implement core positional argument parsing logic [pending]
### Dependencies: 55.1, 55.2
### Description: Modify the argument parser to recognize and process positional arguments according to the specification, while maintaining compatibility with existing named arguments.
### Details:
Update the parser to identify arguments without flags as positional, map them to the correct parameter based on order, and apply appropriate validation. Ensure the implementation handles missing required positional arguments and provides helpful error messages.

## 4. Handle edge cases and error conditions [pending]
### Dependencies: 55.3
### Description: Implement robust handling for edge cases such as too many/few arguments, type mismatches, and ambiguous situations between positional and named arguments.
### Details:
Create comprehensive error handling for scenarios like: providing both positional and named version of the same argument, incorrect argument types, missing required positional arguments, and excess positional arguments. Ensure error messages are clear and actionable for users.

## 5. Update documentation and create usage examples [pending]
### Dependencies: 55.2, 55.3, 55.4
### Description: Update CLI documentation to explain positional argument support and provide clear examples showing how to use positional arguments with different commands.
### Details:
Revise user documentation to include positional argument syntax, update command reference with positional argument information, and create example command snippets showing both positional and named argument usage. Include a migration guide for users transitioning from named-only to positional arguments.

